'use client'

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { blogCategoryService, type CreateBlogCategoryData } from "@/lib/api/blog-categories"

interface BlogCategoryCreateDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data: CreateBlogCategoryData) => void
}

export function BlogCategoryCreateDialog({ open, onOpenChange, onSubmit }: BlogCategoryCreateDialogProps) {
  const [formData, setFormData] = useState({
    title: "",
    slug: "",
    is_active: true,
    is_frontend_show: true
  })
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) {
      newErrors.title = "Title is required"
    } else if (formData.title.length < 2) {
      newErrors.title = "Title must be at least 2 characters"
    } else if (formData.title.length > 100) {
      newErrors.title = "Title must be less than 100 characters"
    }

    if (!formData.slug.trim()) {
      newErrors.slug = "Slug is required"
    } else if (!/^[a-z0-9-]+$/.test(formData.slug)) {
      newErrors.slug = "Slug can only contain lowercase letters, numbers, and hyphens"
    } else if (formData.slug.length < 2) {
      newErrors.slug = "Slug must be at least 2 characters"
    } else if (formData.slug.length > 100) {
      newErrors.slug = "Slug must be less than 100 characters"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)
    try {
      await onSubmit(formData)
      
      // Reset form
      setFormData({
        title: "",
        slug: "",
        is_active: true,
        is_frontend_show: true
      })
      setErrors({})
    } catch (error) {
      console.error('Failed to create blog category:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleTitleChange = (value: string) => {
    setFormData(prev => ({ 
      ...prev, 
      title: value,
      // Auto-generate slug from title if slug is empty or was auto-generated
      slug: prev.slug === blogCategoryService.generateSlug(prev.title) || !prev.slug 
        ? blogCategoryService.generateSlug(value)
        : prev.slug
    }))
    
    // Clear title error when user starts typing
    if (errors.title) {
      setErrors(prev => ({ ...prev, title: "" }))
    }
  }

  const handleSlugChange = (value: string) => {
    // Convert to lowercase and replace spaces with hyphens
    const cleanSlug = value.toLowerCase().replace(/\s+/g, '-')
    setFormData(prev => ({ ...prev, slug: cleanSlug }))
    
    // Clear slug error when user starts typing
    if (errors.slug) {
      setErrors(prev => ({ ...prev, slug: "" }))
    }
  }

  const handleClose = () => {
    setFormData({
      title: "",
      slug: "",
      is_active: true,
      is_frontend_show: true
    })
    setErrors({})
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="w-[95vw] max-w-[525px] max-h-[95vh] rounded-lg overflow-y-auto  sm:mx-auto">
        <DialogHeader>
          <DialogTitle>Create New Blog Category</DialogTitle>
          <DialogDescription>
            Add a new category for organizing your blog posts.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="title">Title *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleTitleChange(e.target.value)}
              placeholder="e.g., Technology, Lifestyle, Business"
              className={errors.title ? "border-destructive" : ""}
            />
            {errors.title && (
              <p className="text-sm text-destructive">{errors.title}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="slug">Slug *</Label>
            <Input
              id="slug"
              value={formData.slug}
              onChange={(e) => handleSlugChange(e.target.value)}
              placeholder="e.g., technology, lifestyle, business"
              className={errors.slug ? "border-destructive" : ""}
            />
            {errors.slug && (
              <p className="text-sm text-destructive">{errors.slug}</p>
            )}
            <p className="text-xs text-muted-foreground">
              URL-friendly version of the title. Only lowercase letters, numbers, and hyphens allowed.
            </p>
          </div>

          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_active"
                checked={formData.is_active}
                onCheckedChange={(checked) => 
                  setFormData(prev => ({ ...prev, is_active: checked === true }))
                }
              />
              <Label htmlFor="is_active" className="text-sm font-normal">
                Active Category
              </Label>
            </div>
            <p className="text-xs text-muted-foreground ml-6">
              Active categories can be used for blog posts
            </p>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_frontend_show"
                checked={formData.is_frontend_show}
                onCheckedChange={(checked) => 
                  setFormData(prev => ({ ...prev, is_frontend_show: checked === true }))
                }
              />
              <Label htmlFor="is_frontend_show" className="text-sm font-normal">
                Show on Frontend
              </Label>
            </div>
            <p className="text-xs text-muted-foreground ml-6">
              Visible categories will appear in navigation and category lists
            </p>
          </div>

          <DialogFooter>
            <Button type="button" className="mb-2 sm:mb-0" variant="outline" onClick={handleClose} disabled={loading}>
              Cancel
            </Button>
            <Button type="submit"  className="mb-2 sm:mb-0" disabled={loading}>
              {loading ? "Creating..." : "Create Category"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
